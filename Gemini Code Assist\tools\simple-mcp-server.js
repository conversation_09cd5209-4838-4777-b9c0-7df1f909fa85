#!/usr/bin/env node

/**
 * مثال بسيط على خادم MCP مخصص
 * هذا المثال يوضح كيفية إنشاء أداة مخصصة لـ Gemini Code Assist
 */

const express = require('express');
const app = express();
const port = 8080;

// إعداد middleware
app.use(express.json());

// إعداد CORS للسماح بالطلبات من VS Code
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// نقطة نهاية للتحقق من حالة الخادم
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        message: 'خادم MCP يعمل بشكل طبيعي',
        timestamp: new Date().toISOString()
    });
});

// نقطة نهاية MCP الرئيسية
app.post('/mcp', (req, res) => {
    const { method, params } = req.body;
    
    try {
        switch (method) {
            case 'tools/list':
                res.json({
                    tools: [
                        {
                            name: 'code_analyzer',
                            description: 'تحليل الكود وتقديم اقتراحات للتحسين',
                            inputSchema: {
                                type: 'object',
                                properties: {
                                    code: { type: 'string', description: 'الكود المراد تحليله' },
                                    language: { type: 'string', description: 'لغة البرمجة' }
                                },
                                required: ['code']
                            }
                        },
                        {
                            name: 'comment_translator',
                            description: 'ترجمة التعليقات بين العربية والإنجليزية',
                            inputSchema: {
                                type: 'object',
                                properties: {
                                    text: { type: 'string', description: 'النص المراد ترجمته' },
                                    targetLanguage: { type: 'string', description: 'اللغة المستهدفة (ar/en)' }
                                },
                                required: ['text', 'targetLanguage']
                            }
                        }
                    ]
                });
                break;
                
            case 'tools/call':
                const { name, arguments: args } = params;
                
                if (name === 'code_analyzer') {
                    const analysis = analyzeCode(args.code, args.language);
                    res.json({ content: [{ type: 'text', text: analysis }] });
                } else if (name === 'comment_translator') {
                    const translation = translateComment(args.text, args.targetLanguage);
                    res.json({ content: [{ type: 'text', text: translation }] });
                } else {
                    res.status(400).json({ error: `أداة غير معروفة: ${name}` });
                }
                break;
                
            default:
                res.status(400).json({ error: `طريقة غير مدعومة: ${method}` });
        }
    } catch (error) {
        console.error('خطأ في معالجة الطلب:', error);
        res.status(500).json({ error: 'خطأ داخلي في الخادم' });
    }
});

// دالة تحليل الكود (مثال بسيط)
function analyzeCode(code, language = 'javascript') {
    const suggestions = [];
    
    // فحوصات بسيطة
    if (code.includes('var ')) {
        suggestions.push('💡 استخدم let أو const بدلاً من var');
    }
    
    if (code.includes('console.log') && !code.includes('// debug')) {
        suggestions.push('🔍 تأكد من إزالة console.log قبل الإنتاج');
    }
    
    if (code.length > 1000) {
        suggestions.push('📏 الكود طويل، فكر في تقسيمه إلى دوال أصغر');
    }
    
    const lines = code.split('\n');
    if (lines.some(line => line.length > 100)) {
        suggestions.push('📐 بعض الأسطر طويلة جداً، فكر في تقسيمها');
    }
    
    if (suggestions.length === 0) {
        return '✅ الكود يبدو جيداً! لا توجد اقتراحات للتحسين.';
    }
    
    return `تحليل الكود:\n\n${suggestions.map(s => `• ${s}`).join('\n')}`;
}

// دالة ترجمة التعليقات (مثال بسيط)
function translateComment(text, targetLanguage) {
    // هذا مثال بسيط - في التطبيق الحقيقي ستحتاج إلى API ترجمة
    const translations = {
        'ar': {
            'function': 'دالة',
            'variable': 'متغير',
            'class': 'فئة',
            'method': 'طريقة',
            'return': 'إرجاع',
            'parameter': 'معامل'
        },
        'en': {
            'دالة': 'function',
            'متغير': 'variable',
            'فئة': 'class',
            'طريقة': 'method',
            'إرجاع': 'return',
            'معامل': 'parameter'
        }
    };
    
    let result = text;
    const dict = translations[targetLanguage] || {};
    
    Object.keys(dict).forEach(key => {
        const regex = new RegExp(key, 'gi');
        result = result.replace(regex, dict[key]);
    });
    
    return `الترجمة إلى ${targetLanguage === 'ar' ? 'العربية' : 'الإنجليزية'}:\n${result}`;
}

// بدء الخادم
app.listen(port, () => {
    console.log(`🚀 خادم MCP يعمل على http://localhost:${port}`);
    console.log(`📋 للتحقق من الحالة: http://localhost:${port}/health`);
});

module.exports = app;
