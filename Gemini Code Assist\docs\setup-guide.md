# دليل إعداد Gemini Code Assist

## المتطلبات الأساسية
- VS Code مع إضافة Gemini Code Assist مثبتة
- Node.js (للأدوات المخصصة)
- Python (اختياري للأدوات المتقدمة)

## خطوات الإعداد

### 1. إعداد ملف GEMINI.md
ملف `GEMINI.md` موجود بالفعل في المجلد الرئيسي ويحتوي على:
- وصف المشروع والأهداف
- قواعد البرمجة المفضلة
- إرشادات للمساعد الذكي

### 2. إعد<PERSON> خوادم MCP (اختياري ومتقدم)

#### الخطوة الأولى: إنشاء ملف الإعدادات
```bash
# إنشاء مجلد .gemini في المجلد الرئيسي للمستخدم
mkdir ~/.gemini

# نسخ ملف الإعدادات المثال
cp configs/mcp-settings-example.json ~/.gemini/settings.json
```

#### الخطوة الثانية: تخصيص الإعدادات
قم بتعديل ملف `~/.gemini/settings.json` حسب احتياجاتك:
- غيّر عناوين URL للخوادم الخاصة بك
- فعّل الخوادم التي تريد استخدامها (`"enabled": true`)
- اضبط الإعدادات حسب تفضيلاتك

### 3. الأدوات المتاحة

#### أدوات مدمجة في Gemini Code Assist:
- **تحليل الكود**: فهم وتحليل الكود الموجود
- **إنشاء الكود**: كتابة كود جديد بناءً على الوصف
- **إصلاح الأخطاء**: اكتشاف وإصلاح الأخطاء البرمجية
- **التوثيق**: إنشاء تعليقات ووثائق للكود
- **الترجمة**: ترجمة التعليقات بين اللغات

#### أدوات مخصصة (تتطلب إعداد MCP):
- **محلل الكود المخصص**: تحليل متقدم للكود
- **مولد الوثائق**: إنشاء وثائق شاملة للمشروع
- **مترجم التعليقات**: ترجمة تلقائية للتعليقات

### 4. نصائح للاستخدام الفعال

#### تحسين فهم السياق:
1. اكتب وصف واضح للمشروع في `GEMINI.md`
2. أضف تعليقات مفيدة في الكود
3. استخدم أسماء متغيرات وصفية

#### التفاعل مع المساعد:
- اطرح أسئلة محددة وواضحة
- اطلب شرح الكود المعقد
- استخدم اللغة العربية للحصول على شروحات بالعربية

### 5. استكشاف الأخطاء

#### مشاكل شائعة وحلولها:
- **عدم فهم السياق**: تأكد من وجود ملف `GEMINI.md` محدث
- **بطء الاستجابة**: تحقق من اتصال الإنترنت
- **أخطاء MCP**: تأكد من صحة عناوين URL في ملف الإعدادات

## الخطوات التالية
1. جرب الميزات الأساسية أولاً
2. اقرأ وثائق Google الرسمية لـ Gemini Code Assist
3. ابدأ بإعداد أدوات MCP بسيطة إذا كنت تحتاجها
4. شارك تجربتك مع المجتمع

## مراجع مفيدة
- [وثائق Gemini Code Assist الرسمية](https://cloud.google.com/gemini/docs)
- [دليل MCP Protocol](https://modelcontextprotocol.io/)
- [أمثلة على خوادم MCP](https://github.com/modelcontextprotocol/servers)
