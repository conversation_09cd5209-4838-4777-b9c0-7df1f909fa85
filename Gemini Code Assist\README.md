# مشروع Gemini Code Assist - الأدوات والميزات

## نظرة عامة
هذا المشروع يهدف إلى تحسين تجربة استخدام Gemini Code Assist في VS Code من خلال إضافة أدوات وميزات مخصصة.

## الملفات والمجلدات

### 📁 الهيكل الأساسي
```
Gemini Code Assist/
├── 📄 GEMINI.md              # ملف التكوين الرئيسي لـ Gemini
├── 📄 README.md              # هذا الملف
├── 📁 configs/               # ملفات التكوين
│   └── mcp-settings-example.json
├── 📁 docs/                  # الوثائق والأدلة
│   └── setup-guide.md
└── 📁 tools/                 # الأدوات المخصصة
    ├── package.json
    └── simple-mcp-server.js
```

### 🔧 الملفات المهمة

#### `GEMINI.md`
ملف التكوين الرئيسي الذي يساعد Gemini Code Assist على فهم مشروعك بشكل أفضل. يحتوي على:
- وصف المشروع والأهداف
- قواعد البرمجة المفضلة
- إرشادات للمساعد الذكي

#### `configs/mcp-settings-example.json`
مثال على ملف إعدادات MCP للأدوات المخصصة. يجب نسخه إلى `~/.gemini/settings.json` وتخصيصه.

#### `tools/simple-mcp-server.js`
مثال على خادم MCP بسيط يوفر أدوات مخصصة مثل:
- تحليل الكود وتقديم اقتراحات
- ترجمة التعليقات بين العربية والإنجليزية

## 🚀 البدء السريع

### 1. إعداد الملفات الأساسية
الملفات جاهزة للاستخدام! ملف `GEMINI.md` سيساعد Gemini على فهم مشروعك بشكل أفضل.

### 2. تجربة الأدوات المخصصة (اختياري)
```bash
# الانتقال إلى مجلد الأدوات
cd tools

# تثبيت التبعيات
npm install

# تشغيل الخادم
npm start
```

### 3. إعداد MCP (متقدم)
```bash
# إنشاء مجلد الإعدادات
mkdir ~/.gemini

# نسخ ملف الإعدادات
cp configs/mcp-settings-example.json ~/.gemini/settings.json

# تعديل الإعدادات حسب احتياجاتك
```

## 📚 الاستخدام

### الميزات الأساسية
- **فهم أفضل للسياق**: ملف `GEMINI.md` يساعد Gemini على فهم مشروعك
- **إرشادات مخصصة**: توجيهات واضحة للمساعد الذكي
- **دعم اللغة العربية**: تفاعل باللغة العربية مع المساعد

### الأدوات المخصصة (تتطلب إعداد MCP)
- **تحليل الكود**: فحص الكود وتقديم اقتراحات للتحسين
- **ترجمة التعليقات**: ترجمة بين العربية والإنجليزية
- **أدوات إضافية**: يمكن إضافة المزيد حسب الحاجة

## 🛠️ التخصيص

### إضافة أدوات جديدة
1. عدّل ملف `tools/simple-mcp-server.js`
2. أضف الأداة الجديدة في قائمة `tools/list`
3. اكتب منطق الأداة في `tools/call`

### تخصيص الإعدادات
1. عدّل ملف `~/.gemini/settings.json`
2. أضف خوادم MCP جديدة
3. اضبط الإعدادات العامة

## 📖 المراجع
- [دليل الإعداد التفصيلي](docs/setup-guide.md)
- [وثائق Gemini Code Assist](https://cloud.google.com/gemini/docs)
- [بروتوكول MCP](https://modelcontextprotocol.io/)

## 🤝 المساهمة
هذا مشروع تجريبي لتحسين تجربة البرمجة. يمكنك:
- إضافة أدوات جديدة
- تحسين الأدوات الموجودة
- مشاركة تجربتك واقتراحاتك

## 📝 ملاحظات
- المشروع في مرحلة التطوير والتجريب
- الأدوات المخصصة اختيارية ومتقدمة
- ابدأ بالميزات الأساسية أولاً

---
**تم إنشاؤه بمساعدة Gemini Code Assist** 🤖✨
