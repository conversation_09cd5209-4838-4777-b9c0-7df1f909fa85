# مشروع Gemini Code Assist - الأدوات والميزات

## نظرة عامة
هذا المشروع يهدف إلى تحسين تجربة استخدام Gemini Code Assist في VS Code من خلال إضافة أدوات وميزات مخصصة.

## الملفات والمجلدات

### 📁 الهيكل الأساسي
```
Gemini Code Assist/
├── 📄 GEMINI.md              # ملف التكوين الرئيسي لـ Gemini
├── 📄 README.md              # هذا الملف
├── 📁 configs/               # ملفات التكوين
│   └── mcp-settings-example.json
├── 📁 docs/                  # الوثائق والأدلة
│   └── setup-guide.md
└── 📁 tools/                 # الأدوات المخصصة
    ├── package.json
    └── simple-mcp-server.js
```

### 🔧 الملفات المهمة

#### `GEMINI.md`
ملف التكوين الرئيسي الذي يساعد Gemini Code Assist على فهم مشروعك بشكل أفضل. يحتوي على:
- وصف المشروع والأهداف
- قواعد البرمجة المفضلة
- إرشادات للمساعد الذكي

#### `configs/mcp-settings-example.json`
مثال على ملف إعدادات MCP للأدوات المخصصة. يجب نسخه إلى `~/.gemini/settings.json` وتخصيصه.

#### `tools/simple-mcp-server.js`
مثال على خادم MCP بسيط يوفر أدوات مخصصة مثل:
- تحليل الكود وتقديم اقتراحات
- ترجمة التعليقات بين العربية والإنجليزية

## 🚀 البدء السريع

### 1. إعداد الملفات الأساسية
الملفات جاهزة للاستخدام! ملف `GEMINI.md` سيساعد Gemini على فهم مشروعك بشكل أفضل.

### 2. تجربة الأدوات الرسمية
```
# في VS Code، افتح Gemini Code Assist Chat واكتب:
@GitHub list issues assigned to me
@GoogleDocs list my docs
@MongoDB how can I optimize my query?
```

### 3. إعداد الأدوات (عند الحاجة)
- اتبع التعليمات التي تظهر عند استخدام أداة لأول مرة
- قم بالمصادقة للخدمات المطلوبة
- امنح الصلاحيات اللازمة

## 📚 الاستخدام

### الأدوات المتاحة رسمياً

#### 🔧 إدارة المشاريع:
- **@GitHub**: `@GitHub list my open pull requests`
- **@GitLab**: `@GitLab list issues assigned to me`
- **@AtlassianRovo**: `@AtlassianRovo get tasks assigned to me`

#### 📄 المستندات:
- **@GoogleDocs**: `@GoogleDocs summarize document "Requirements"`

#### 🗄️ قواعد البيانات:
- **@MongoDB**: `@MongoDB show connection examples`
- **@Neo4j**: `@Neo4j How do I configure the driver?`
- **@Redis**: `@Redis what is Redis Cloud?`

#### 🔍 المراقبة والأمان:
- **@NewRelic**: `@NewRelic how do I install the agent?`
- **@Sentry**: `@Sentry list issues in project my-project`
- **@Snyk**: `@Snyk scan for issues`

### الميزات الأساسية
- **فهم أفضل للسياق**: ملف `GEMINI.md` يساعد Gemini على فهم مشروعك
- **إرشادات مخصصة**: توجيهات واضحة للمساعد الذكي
- **دعم اللغة العربية**: تفاعل باللغة العربية مع المساعد
- **أدوات متكاملة**: الوصول لخدمات خارجية من داخل IDE

## 🛠️ التخصيص

### إعداد الأدوات الرسمية
1. **للبدء**: اكتب `@ToolName` في Gemini Chat
2. **المصادقة**: اتبع الروابط المقدمة للمصادقة
3. **الاستخدام**: جرب الأوامر المقترحة

### إدارة الصلاحيات
- **إضافة أدوات**: [Agents and Tools Page](https://codeassist.google.com/agents-tools)
- **إلغاء المصادقة**: من نفس الصفحة اختر "Disconnect"
- **تغيير الحساب**: قم بإلغاء المصادقة ثم أعد الإعداد

### تخصيص ملف GEMINI.md
- أضف وصف مشروعك
- حدد قواعد البرمجة المفضلة
- اكتب إرشادات للمساعد الذكي

## 📖 المراجع
- [دليل الإعداد التفصيلي](docs/setup-guide.md)
- [وثائق Gemini Code Assist](https://cloud.google.com/gemini/docs)
- [بروتوكول MCP](https://modelcontextprotocol.io/)

## 🤝 المساهمة
هذا مشروع تجريبي لتحسين تجربة البرمجة. يمكنك:
- إضافة أدوات جديدة
- تحسين الأدوات الموجودة
- مشاركة تجربتك واقتراحاتك

## 📝 ملاحظات
- المشروع في مرحلة التطوير والتجريب
- الأدوات المخصصة اختيارية ومتقدمة
- ابدأ بالميزات الأساسية أولاً

---
**تم إنشاؤه بمساعدة Gemini Code Assist** 🤖✨
