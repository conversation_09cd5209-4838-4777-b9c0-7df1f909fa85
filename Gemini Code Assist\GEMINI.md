# مشروع Gemini Code Assist

## وصف المشروع
هذا المشروع مخصص لتطوير وتحسين تجربة استخدام Gemini Code Assist في VS Code. الهدف هو إضافة أدوات وميزات مخصصة لتحسين الإنتاجية في البرمجة.

## الأهداف الحالية
- إعداد وتكوين Gemini Code Assist
- إضافة أدوات مخصصة عبر MCP servers
- تحسين فهم السياق للمشروع
- تطوير ميزات مساعدة للبرمجة

## قواعد البرمجة المفضلة
- استخدام أسماء متغيرات واضحة ووصفية
- كتابة تعليقات مفيدة باللغة العربية أو الإنجليزية
- اتباع أفضل الممارسات لكل لغة برمجة
- كتابة كود قابل للقراءة والصيانة

## الأدوات والتقنيات المستخدمة
- VS Code كبيئة التطوير الرئيسية
- Gemini Code Assist للمساعدة في البرمجة
- Git لإدارة الإصدارات
- MCP (Model Context Protocol) للأدوات المخصصة

## هيكل المشروع
```
Gemini Code Assist/
├── GEMINI.md          # ملف التكوين الرئيسي
├── tools/             # مجلد الأدوات المخصصة
├── configs/           # ملفات التكوين
└── docs/              # الوثائق والمراجع
```

## إرشادات للمساعد الذكي
عند العمل على هذا المشروع:
1. اقترح حلول عملية وقابلة للتطبيق
2. اشرح الخطوات بوضوح
3. قدم أمثلة عملية عند الحاجة
4. احرص على التوافق مع VS Code و Gemini Code Assist
5. استخدم اللغة العربية في التوضيحات عند الطلب

## ملاحظات مهمة
- هذا المشروع في مرحلة التطوير والتجريب
- يُفضل اختبار أي تغييرات قبل التطبيق النهائي
- الهدف هو تحسين تجربة البرمجة وزيادة الإنتاجية