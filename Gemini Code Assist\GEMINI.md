# مشروع Gemini Code Assist

## وصف المشروع
هذا المشروع مخصص لتطوير وتحسين تجربة استخدام Gemini Code Assist في VS Code. الهدف هو الاستفادة من الأدوات المتاحة رسمياً وتحسين فهم السياق للمشروع.

## الأهداف الحالية
- إعداد وتكوين Gemini Code Assist والأدوات المتاحة
- استخدام الأدوات الرسمية مثل @GitHub و @GoogleDocs و @MongoDB
- تحسين فهم السياق للمشروع عبر ملف GEMINI.md
- تطوير سير عمل فعال باستخدام الأدوات المتكاملة

## قواعد البرمجة المفضلة
- استخدام أسماء متغيرات واضحة ووصفية
- كتابة تعليقات مفيدة باللغة العربية أو الإنجليزية
- اتباع أفضل الممارسات لكل لغة برمجة
- كتابة كود قابل للقراءة والصيانة

## الأدوات والتقنيات المستخدمة
- VS Code كبيئة التطوير الرئيسية
- Gemini Code Assist للمساعدة في البرمجة
- الأدوات الرسمية: @GitHub, @GitLab, @GoogleDocs, @MongoDB, @Redis, @Sentry, @Snyk
- Git لإدارة الإصدارات

## الأدوات المتاحة في المشروع
### أدوات إدارة المشاريع:
- @GitHub: إدارة المشاكل والـ pull requests
- @GitLab: إدارة المشاريع والـ merge requests
- @AtlassianRovo: إدارة المهام

### أدوات قواعد البيانات:
- @MongoDB: استعلامات وتحسين قواعد البيانات
- @Redis: قواعد البيانات في الذاكرة
- @Neo4j: قواعد بيانات الرسوم البيانية

### أدوات المراقبة والأمان:
- @Sentry: تتبع الأخطاء
- @NewRelic: مراقبة الأداء
- @Snyk: فحص الأمان

## هيكل المشروع
```
Gemini Code Assist/
├── GEMINI.md              # ملف التكوين الرئيسي
├── README.md              # دليل المشروع
├── configs/               # ملفات التكوين (للمرجع)
├── docs/                  # الوثائق والأدلة
│   ├── setup-guide.md     # دليل الإعداد
│   └── tools-examples.md  # أمثلة على الأدوات
└── tools/                 # أمثلة على أدوات مخصصة (للمرجع)
```

## أمثلة على الاستخدام
### سير عمل نموذجي:
```
1. @GitHub list issues assigned to me
2. @GoogleDocs read requirements document
3. @MongoDB design database queries
4. @Sentry check for related errors
5. @Snyk scan for security issues
```

### أوامر مفيدة:
- `@GitHub find code relating to authentication`
- `@MongoDB how can I optimize my query?`
- `@Redis show caching examples`
- `@Sentry list recent errors`

## إرشادات للمساعد الذكي
عند العمل على هذا المشروع:
1. اقترح استخدام الأدوات المناسبة (@GitHub, @MongoDB, إلخ)
2. اشرح الخطوات بوضوح مع أمثلة عملية
3. قدم أوامر محددة للأدوات المختلفة
4. احرص على التوافق مع VS Code و Gemini Code Assist
5. استخدم اللغة العربية في التوضيحات عند الطلب
6. اقترح سير عمل متكامل باستخدام عدة أدوات

## ملاحظات مهمة
- الأدوات الرسمية متاحة مباشرة في Gemini Code Assist
- تحتاج مصادقة لكل أداة عند الاستخدام الأول
- راجع docs/tools-examples.md للحصول على أمثلة شاملة
- الهدف هو تحسين تجربة البرمجة وزيادة الإنتاجية